:root {
  --primary-red: #dc2626;
  --primary-red-hover: #b91c1c;
  --primary-red-light: #ef4444;
  --background-gray: #f3f4f6;
  --text-dark: #222;
  --text-gray: #666;
  --text-light-gray: #9ca3af;
  --white: #fff;
  --dark-bg: #111827;
  --border-gray: #1f2937;
  --red-light-bg: #fee2e2;
  --transition-fast: 0.2s;
  --border-radius-full: 9999rem;
  --border-radius-lg: 1rem;
  --spacing-xs: 0.5rem;  
  --spacing-sm: 0.75rem;  
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;   
  --spacing-2xl: 3rem;  
  --spacing-3xl: 4rem;   
}

body {
  font-family: sans-serif;
  background: var(--background-gray);
  margin: 0;
  padding: 0;
}

.container {
  max-width: 75rem;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.text-center {
  text-align: center;
}

.btn {
  display: inline-block;
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--border-radius-full);
  font-weight: bold;
  text-decoration: none;
  text-align: center;
  border: none;
  cursor: pointer;
  transition: all var(--transition-fast);

  &.btn-primary {
    background: var(--primary-red);
    color: var(--white);

    &:hover {
      background: var(--primary-red-hover);
    }
  }

  &.btn-outline {
    background: transparent;
    border: 0.125rem solid var(--white);
    color: var(--white);

    &:hover {
      background: var(--white);
      color: var(--text-dark);
    }
  }

  &.btn-white {
    background: var(--white);
    color: var(--primary-red);

    &:hover {
      background: var(--background-gray);
    }
  }
}

.title {
  font-size: 2rem;
  font-weight: bold;
  text-align: center;
}

.title-section {
  margin-bottom: var(--spacing-2xl);
}

.title-courses {
  margin-bottom: var(--spacing-md);
}

header {
  background: var(--white);
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.05); 
  position: fixed;
  width: 100%;
  z-index: 50;

  .header-flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
  }

  .logo {
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary-red);
    text-decoration: none;

    span {
      color: var(--text-dark);
    }
  }
}

.footer-logo {
  font-size: 2rem;
  font-weight: bold;
  color: var(--primary-red);
  text-decoration: none;
  margin-bottom: var(--spacing-md);

  span {
    color: var(--white);
  }
}

nav {
  display: none;

  .nav-links {
    display: flex;
    gap: var(--spacing-xl);

    .nav-link {
      color: var(--text-dark);
      text-decoration: none;
      font-weight: 500;
      transition: color var(--transition-fast);

      &:hover {
        color: var(--primary-red);
      }
    }
  }
}

.hamburger {
  display: block;
  background: none;
  border: none;
  font-size: 2rem;
  color: var(--text-dark);
  cursor: pointer;
}

.mobile-menu {
  display: none;
  position: fixed;
  inset: 0;
  background: var(--white);
  z-index: 100;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  &.active {
    display: flex;
  }

  a {
    font-size: 2rem;
    color: var(--text-dark);
    text-decoration: none;
    margin: var(--spacing-md) 0;
    transition: color var(--transition-fast);

    &:hover {
      color: var(--primary-red);
    }
  }

  .close-menu {
    position: absolute;
    top: 1.25rem;  
    right: 1.25rem;
    font-size: 2rem;
    color: var(--text-dark);
    cursor: pointer;
  }
}

.hero-image {
  min-height: 100vh;
  display: flex;
  align-items: center;
  padding-top: var(--spacing-3xl);
  background: var(--text-dark) url("../img/starter.jpg") center/cover no-repeat;

  .hero-content {
    color: var(--white);
    max-width: 43.75rem;

    .hero-title {
      font-size: 2.5rem;
      font-weight: bold;
      margin-bottom: var(--spacing-lg);
    }

    .hero-desc {
      font-size: 1.35rem;
      margin-bottom: var(--spacing-xl);
    }

    .hero-buttons {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-md);
      max-width: 25rem; 
    }
  }
}

section {
  padding: var(--spacing-3xl) 0;
}

.why-choose {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-xl);

  .why-card {
    background: var(--background-gray);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    text-align: center;

    .why-icon {
      background: var(--red-light-bg);
      width: 5rem; 
      height: 5rem;
      margin: 0 auto var(--spacing-md);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      i {
        color: var(--primary-red);
        font-size: 2rem;
      }
    }

    .why-title {
      font-size: 1.25rem;
      font-weight: bold;
      margin-bottom: var(--spacing-sm);
    }

    .why-desc {
      color: var(--text-gray);
    }
  }
}

.equipement-group {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-xl);
  margin-top: var(--spacing-3xl);
  align-items: center;

  h3 {
    font-size: var(--spacing-xl);
    color: var(--primary-red);
    font-weight: bold;
  }

  p, div p {
    color: var(--text-gray);
  }
}

.equipement img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.courses-desc {
  text-align: center;
  color: var(--text-gray);
  max-width: 37.5rem;
  margin: 0 auto var(--spacing-2xl);
}

.courses-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-xl);

  .class-card {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.08);
    text-align: left;

    img {
      width: 100%;
      height: 12rem;
      object-fit: cover;
    }

    .class-card-content {
      padding: var(--spacing-lg);

      .class-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-sm);

        .class-tag {
          background: var(--red-light-bg);
          color: var(--primary-red);
          padding: 0.25rem var(--spacing-sm);
          border-radius: var(--border-radius-full);
          font-size: 0.9rem;
          font-weight: bold;
        }

        .class-time {
          color: var(--text-gray);
          font-size: 0.9rem;
        }
      }

      .class-title {
        font-size: 1.25rem;
        font-weight: bold;
        margin-bottom: var(--spacing-xs);
      }

      .class-desc {
        color: var(--text-gray);
        margin-bottom: var(--spacing-md);
      }

      .class-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .class-price {
          font-weight: bold;
          color: var(--primary-red);
        }

        .class-link {
          color: var(--primary-red);
          font-weight: bold;
          text-decoration: none;
          transition: color var(--transition-fast);

          &:hover {
            color: var(--primary-red-hover);
          }
        }
      }
    }
  }
}

.courses-btn {
  margin-top: var(--spacing-2xl);
}

.cta-section {
  background: var(--primary-red);
  color: var(--white);
  text-align: center;
  padding: var(--spacing-3xl) 0;

  .cta-title {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: var(--spacing-lg);
  }

  .cta-desc {
    font-size: 1.25rem;
    margin-bottom: var(--spacing-xl);
    max-width: 37.5rem; 
    margin-left: auto;
    margin-right: auto;
  }
}

.schedule-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 30px;
  max-width: 1200px;
  width: 100%;

  h1 {
    text-align: center;
    color: #dc2626;
    font-size: 2.5em;
    margin-bottom: 30px;
    text-transform: uppercase;
    letter-spacing: 2px;
    font-weight: bold;
  }

  .schedule-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden;

    th {
      background: linear-gradient(135deg, #dc2626, #b91c1c);
      color: white;
      padding: 18px 15px;
      text-align: center;
      font-weight: bold;
      text-transform: uppercase;
      letter-spacing: 1px;
      font-size: 0.9em;
    }

    td {
      padding: 15px;
      text-align: center;
      border-bottom: 1px solid #e5e5e5;
      font-size: 0.85em;
      transition: background-color 0.3s ease;
    }

    tr:nth-child(even) {
      background-color: #f9f9f9;
    }

    tr:hover {
      background-color: #fee2e2;
      transform: scale(1.01);
      transition: all 0.3s ease;
    }

    .time-slot {
      background-color: #1f2937 !important;
      color: white;
      font-weight: bold;
      font-size: 0.9em;
    }

    .class-cell {
      font-weight: 600;
      color: #1f2937;
      position: relative;

      .instructor {
        font-size: 0.75em;
        color: #6b7280;
        font-style: italic;
        margin-top: 3px;
      }
    }

    .boxing { 
      background-color: #fee2e2; 
      color: #dc2626; 
    }
    .crossfit { 
      background-color: #fef3c7; 
      color: #d97706; 
    }
    .yoga { 
      background-color: #d1fae5; 
      color: #059669; 
    }
    .cardio { 
      background-color: #dbeafe; 
      color: #2563eb; 
    }
    .strength { 
      background-color: #f3e8ff; 
      color: #7c3aed; 
    }
    .stretching { 
      background-color: #fce7f3; 
      color: #db2777; 
    }
  }

  @media (max-width: 768px) {
    .schedule-table {
      font-size: 0.8em;

      th,
      td {
        padding: 10px 8px;
      }
    }

    h1 {
      font-size: 2em;
    }
  }
}

.hours-wrapper {
  border: 0.1875rem solid black;
  border-radius: 0.5rem;
  padding: 1.875rem;
  max-width: 31.25rem;
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
  margin: 0 auto;

  h1 {
    color: black;
    text-align: center;
    margin-bottom: 1.875rem;
    font-size: 2.2em;
    border-bottom: 0.1875rem solid var(--primary-red-light);
    padding-bottom: 0.625rem;
  }

  .hours-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1.25rem;

    th {
      background-color: var(--primary-red);
      color: white;
      padding: 0.9375rem;
      text-align: left;
      font-weight: bold;
      font-size: 1.1em;
    }

    td {
      padding: 0.9375rem;
      border-bottom: 0.125rem solid black;
      font-size: 1.1em;

      &.day-label {
        font-weight: bold;
        color: black;
        background-color: #f9f9f9;
      }

      &.time-slot {
        color: black;

        &.open-time {
          font-weight: bold;
        }

        &.closed-time {
          color: var(--primary-red);
          font-weight: bold;
          text-transform: uppercase;
        }
      }
    }
  }

  .hours-note {
    background-color: var(--dark-bg);
    color: white;
    padding: 0.9375rem;
    border-radius: 0.3125rem;
    margin-top: 1.25rem;
    text-align: center;

    .note-highlight {
      color: var(--primary-red);
      font-weight: bold;
    }
  }
}


footer {
  background: var(--dark-bg);
  color: var(--white);
  padding: var(--spacing-2xl) 0;

  .footer-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
  }

  .footer-social {
    display: flex;
    gap: var(--spacing-md);

    a {
      color: var(--white);
      text-decoration: none;
      transition: color var(--transition-fast);
      font-size: 1.2rem;

      &:hover {
        color: var(--primary-red-light);
      }
    }
  }

  .footer-title {
    font-size: 1.1rem;
    font-weight: bold;
    margin-bottom: var(--spacing-md);
  }

  .footer-links,
  .footer-hours,
  .footer-contact {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      margin-bottom: var(--spacing-xs);

      a {
        color: var(--white);
        text-decoration: none;
        transition: color var(--transition-fast);

        &:hover {
          color: var(--primary-red-light);
        }
      }
    }
  }

  .footer-bottom {
    border-top: 0.0625rem solid var(--border-gray);
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-xl);
    text-align: center;
    color: var(--text-light-gray);
    font-size: 0.95rem;
  }
}

@media (min-width: 40rem) {
  .hero-buttons {
    flex-direction: row;
  }
}

@media (min-width: 48rem) {
  nav {
    display: block;
  }

  .hamburger {
    display: none;
  }

  .mobile-menu {
    display: none !important;
  }

  .why-choose {
    grid-template-columns: repeat(3, 1fr);
  }

  .courses-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .footer-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 64rem) {
  .courses-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}